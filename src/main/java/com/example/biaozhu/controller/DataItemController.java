package com.example.biaozhu.controller;

import com.example.biaozhu.entity.DataItem;
import com.example.biaozhu.entity.Dataset;
import com.example.biaozhu.entity.User;
import com.example.biaozhu.payload.request.DataItemCreateDTO;
import com.example.biaozhu.payload.response.MessageResponse;
import com.example.biaozhu.repository.DataItemRepository;
import com.example.biaozhu.repository.DatasetRepository;
import com.example.biaozhu.repository.UserRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * 数据项控制器
 * 处理数据项的增删改查
 */
@RestController
@RequestMapping("/api/dataitems")
public class DataItemController {

    private final DataItemRepository dataItemRepository;
    private final DatasetRepository datasetRepository;
    private final UserRepository userRepository;

    @Autowired
    public DataItemController(
            DataItemRepository dataItemRepository,
            DatasetRepository datasetRepository,
            UserRepository userRepository) {
        this.dataItemRepository = dataItemRepository;
        this.datasetRepository = datasetRepository;
        this.userRepository = userRepository;
    }

    /**
     * 检查用户是否对数据集有权限
     * @param user 用户
     * @param dataset 数据集
     * @return 是否有权限
     */
    private boolean hasPermission(User user, Dataset dataset) {
        // 如果是管理员，直接返回true
        if (user.getRoles().stream().anyMatch(role -> role.getName().toString().equals("ADMIN"))) {
            return true;
        }
        
        // 如果是数据集的创建者，也返回true
        if (dataset.getCreator() != null && dataset.getCreator().getId().equals(user.getId())) {
            return true;
        }
        
        // 其他情况，返回false
        return false;
    }

    /**
     * 获取数据集下的所有数据项
     */
    @GetMapping("/dataset/{datasetId}")
    public ResponseEntity<?> getDataItemsByDatasetId(@PathVariable Long datasetId) {
        try {
            // 检查数据集是否存在
            if (!datasetRepository.existsById(datasetId)) {
                return ResponseEntity.badRequest().body(new MessageResponse("数据集不存在"));
            }

            // 使用自定义JPQL查询获取数据项列表
            List<DataItem> dataItems = dataItemRepository.findUnannotatedItemsByDatasetId(datasetId);
            
            // 转换为简单的Map列表
            List<Map<String, Object>> result = dataItems.stream().map(item -> {
                Map<String, Object> map = new HashMap<>();
                map.put("id", item.getId());
                map.put("name", item.getName());
                map.put("type", item.getType());
                map.put("filePath", item.getFilePath());
                map.put("fileSize", item.getFileSize());
                map.put("fileType", item.getFileType());
                map.put("annotated", item.isAnnotated());
                map.put("createdAt", item.getCreatedAt());
                return map;
            }).collect(Collectors.toList());

            return ResponseEntity.ok(result);
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseEntity.badRequest().body(new MessageResponse("获取数据项失败: " + e.getMessage()));
        }
    }

    /**
     * 获取数据集下的数据项（分页）
     */
    @GetMapping("/datasets/{datasetId}/items")
    public ResponseEntity<?> getDataItemsByDatasetIdPaged(
            @PathVariable Long datasetId,
            @RequestParam(required = false, defaultValue = "0") int page,
            @RequestParam(required = false, defaultValue = "10") int size) {
        
        Map<String, Object> response = new HashMap<>();
        
        try {
            // 验证数据集是否存在
            Optional<Dataset> datasetOpt = datasetRepository.findById(datasetId);
            if (!datasetOpt.isPresent()) {
                response.put("success", false);
                response.put("message", "数据集不存在");
                return ResponseEntity.status(HttpStatus.NOT_FOUND).body(response);
            }
            
            // 创建分页请求
            Pageable pageable = PageRequest.of(page, size, Sort.by("createdAt").descending());
            
            // 查询数据项
            Page<DataItem> dataItems = dataItemRepository.findByDatasetId(datasetId, pageable);
            
            response.put("success", true);
            response.put("dataItems", dataItems.getContent());
            response.put("currentPage", dataItems.getNumber());
            response.put("totalItems", dataItems.getTotalElements());
            response.put("totalPages", dataItems.getTotalPages());
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "获取数据项失败: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    /**
     * 创建新的数据项
     */
    @PostMapping("/datasets/{datasetId}/items")
    public ResponseEntity<?> createDataItem(
            @PathVariable Long datasetId,
            @Valid @RequestBody DataItemCreateDTO dataItemDTO,
            @AuthenticationPrincipal User currentUser) {
        
        Map<String, Object> response = new HashMap<>();
        
        try {
            // 验证数据集是否存在
            Optional<Dataset> datasetOpt = datasetRepository.findById(datasetId);
            if (datasetOpt.isPresent()) {
                response.put("success", false);
                response.put("message", "数据集不存在");
                return ResponseEntity.status(HttpStatus.NOT_FOUND).body(response);
            }
            
            Dataset dataset = datasetOpt.get();
            
            // 检查用户权限
            if (!hasPermission(currentUser, dataset)) {
                response.put("success", false);
                response.put("message", "没有权限在此数据集中创建数据项");
                return ResponseEntity.status(HttpStatus.FORBIDDEN).body(response);
            }
            
            // 创建新的数据项
            DataItem dataItem = new DataItem();
            dataItem.setName(dataItemDTO.getName());
            dataItem.setType(dataItemDTO.getType());
            dataItem.setFilePath(dataItemDTO.getFilePath());
            dataItem.setFileSize(dataItemDTO.getFileSize());
            dataItem.setFileType(dataItemDTO.getFileType());
            dataItem.setContent(dataItemDTO.getContent());
            dataItem.setMetadata(dataItemDTO.getMetadata());
            dataItem.setAnnotated(false);
            dataItem.setSplitType(dataItemDTO.getSplitType());
            dataItem.setIdentifier(dataItemDTO.getIdentifier());
            dataItem.setDataset(dataset);
            dataItem.setCreator(currentUser);
            
            // 保存数据项
            DataItem savedDataItem = dataItemRepository.save(dataItem);
            
            // 更新数据集项目计数
            long count = dataItemRepository.countByDatasetId(datasetId);
            dataset.setItemCount((int)count);
            datasetRepository.save(dataset);
            
            response.put("success", true);
            response.put("message", "数据项创建成功");
            
            // 使用HashMap代替Map.of (Map.of有参数数量限制)
            Map<String, Object> dataItemMap = new HashMap<>();
            dataItemMap.put("id", savedDataItem.getId());
            dataItemMap.put("name", savedDataItem.getName());
            dataItemMap.put("type", savedDataItem.getType());
            dataItemMap.put("filePath", savedDataItem.getFilePath());
            dataItemMap.put("fileSize", savedDataItem.getFileSize());
            dataItemMap.put("fileType", savedDataItem.getFileType());
            dataItemMap.put("createdAt", savedDataItem.getCreatedAt());
            
            response.put("dataItem", dataItemMap);
            
            return ResponseEntity.status(HttpStatus.CREATED).body(response);
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "创建数据项失败: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    /**
     * 上传并创建新数据项
     */
    @PostMapping("/dataset/{datasetId}/upload")
    @PreAuthorize("isAuthenticated()")
    public ResponseEntity<?> uploadDataItem(
            @PathVariable Long datasetId,
            @RequestParam("file") MultipartFile file) {
        try {
            // 获取当前用户
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            User currentUser = userRepository.findByUsername(authentication.getName())
                    .orElseThrow(() -> new RuntimeException("找不到当前用户"));

            // 查找数据集
            Dataset dataset = datasetRepository.findById(datasetId)
                    .orElseThrow(() -> new RuntimeException("找不到数据集，ID: " + datasetId));

            // 创建上传目录
            String uploadDir = "uploads/datasets/" + datasetId;
            Path uploadPath = Paths.get(uploadDir);
            if (!Files.exists(uploadPath)) {
                Files.createDirectories(uploadPath);
            }

            // 生成唯一文件名并保存文件
            String originalFilename = file.getOriginalFilename();
            String uniqueFileName = UUID.randomUUID().toString() + "_" + originalFilename;
            Path filePath = uploadPath.resolve(uniqueFileName);
            Files.copy(file.getInputStream(), filePath);

            // 创建数据项实体
            DataItem dataItem = new DataItem();
            dataItem.setName(originalFilename);
            dataItem.setDataset(dataset);
            dataItem.setCreator(currentUser);
            dataItem.setCreatedAt(LocalDateTime.now());
            dataItem.setUpdatedAt(LocalDateTime.now());
            
            // 根据文件扩展名判断类型
            String fileExtension = originalFilename.toLowerCase();
            if (fileExtension.endsWith(".jpg") || fileExtension.endsWith(".jpeg") || 
                fileExtension.endsWith(".png") || fileExtension.endsWith(".gif")) {
                dataItem.setType("IMAGE");
            } else if (fileExtension.endsWith(".txt") || fileExtension.endsWith(".csv") || 
                      fileExtension.endsWith(".json")) {
                dataItem.setType("TEXT");
            } else if (fileExtension.endsWith(".mp3") || fileExtension.endsWith(".wav")) {
                dataItem.setType("AUDIO");
            } else if (fileExtension.endsWith(".mp4") || fileExtension.endsWith(".avi")) {
                dataItem.setType("VIDEO");
            } else {
                dataItem.setType("TEXT"); // 默认为文本类型
            }
            
            // 设置文件相关信息
            dataItem.setFilePath(filePath.toString());
            dataItem.setFileSize(file.getSize());
            dataItem.setFileType(file.getContentType());
            dataItem.setSplitType("UNASSIGNED");
            dataItem.setAnnotated(false);

            // 保存数据项
            DataItem savedDataItem = dataItemRepository.save(dataItem);

            // 更新数据集项目计数
            long itemCount = dataItemRepository.countByDatasetId(datasetId);
            dataset.setItemCount((int)itemCount);
            datasetRepository.save(dataset);

            // 返回创建的数据项信息
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "数据项创建成功");
            
            // 使用HashMap代替Map.of (Map.of有参数数量限制)
            Map<String, Object> dataItemMap = new HashMap<>();
            dataItemMap.put("id", savedDataItem.getId());
            dataItemMap.put("name", savedDataItem.getName());
            dataItemMap.put("type", savedDataItem.getType());
            dataItemMap.put("filePath", savedDataItem.getFilePath());
            dataItemMap.put("fileSize", savedDataItem.getFileSize());
            dataItemMap.put("fileType", savedDataItem.getFileType());
            dataItemMap.put("createdAt", savedDataItem.getCreatedAt());
            
            response.put("dataItem", dataItemMap);

            return ResponseEntity.ok(response);
        } catch (IOException e) {
            e.printStackTrace();
            return ResponseEntity.badRequest().body(new MessageResponse("文件保存失败: " + e.getMessage()));
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseEntity.badRequest().body(new MessageResponse("创建数据项失败: " + e.getMessage()));
        }
    }

    /**
     * 公开上传端点 - 不需要认证
     */
    @PostMapping("/public/upload/{datasetId}")
    public ResponseEntity<?> publicUploadDataItem(
            @PathVariable Long datasetId,
            @RequestParam("file") MultipartFile file) {
        try {
            System.out.println("公开数据项上传端点被调用");
            System.out.println("数据集ID: " + datasetId);
            System.out.println("文件名: " + file.getOriginalFilename());
            
            // 查找数据集
            Dataset dataset = datasetRepository.findById(datasetId)
                    .orElseThrow(() -> new RuntimeException("找不到数据集，ID: " + datasetId));
            
            // 获取admin用户作为创建者
            User admin = userRepository.findByUsername("admin")
                    .orElseThrow(() -> new RuntimeException("找不到管理员用户"));

            // 创建上传目录
            String uploadDir = "uploads/datasets/" + datasetId;
            Path uploadPath = Paths.get(uploadDir);
            if (!Files.exists(uploadPath)) {
                Files.createDirectories(uploadPath);
            }

            // 生成唯一文件名并保存文件
            String originalFilename = file.getOriginalFilename();
            String uniqueFileName = UUID.randomUUID().toString() + "_" + originalFilename;
            Path filePath = uploadPath.resolve(uniqueFileName);
            Files.copy(file.getInputStream(), filePath);

            // 创建数据项实体
            DataItem dataItem = new DataItem();
            dataItem.setName(originalFilename);
            dataItem.setDataset(dataset);
            dataItem.setCreator(admin);
            dataItem.setCreatedAt(LocalDateTime.now());
            dataItem.setUpdatedAt(LocalDateTime.now());
            
            // 根据文件扩展名判断类型
            String fileExtension = originalFilename.toLowerCase();
            if (fileExtension.endsWith(".jpg") || fileExtension.endsWith(".jpeg") || 
                fileExtension.endsWith(".png") || fileExtension.endsWith(".gif")) {
                dataItem.setType("IMAGE");
            } else if (fileExtension.endsWith(".txt") || fileExtension.endsWith(".csv") || 
                      fileExtension.endsWith(".json")) {
                dataItem.setType("TEXT");
            } else if (fileExtension.endsWith(".mp3") || fileExtension.endsWith(".wav")) {
                dataItem.setType("AUDIO");
            } else if (fileExtension.endsWith(".mp4") || fileExtension.endsWith(".avi")) {
                dataItem.setType("VIDEO");
            } else {
                dataItem.setType("TEXT"); // 默认为文本类型
            }
            
            // 设置文件相关信息
            dataItem.setFilePath(filePath.toString());
            dataItem.setFileSize(file.getSize());
            dataItem.setFileType(file.getContentType());
            dataItem.setSplitType("UNASSIGNED");
            dataItem.setAnnotated(false);

            // 保存数据项
            DataItem savedDataItem = dataItemRepository.save(dataItem);

            // 更新数据集项目计数
            long itemCount = dataItemRepository.countByDatasetId(datasetId);
            dataset.setItemCount((int)itemCount);
            datasetRepository.save(dataset);

            // 返回创建的数据项信息
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "数据项创建成功");
            
            // 使用HashMap代替Map.of
            Map<String, Object> dataItemMap = new HashMap<>();
            dataItemMap.put("id", savedDataItem.getId());
            dataItemMap.put("name", savedDataItem.getName());
            dataItemMap.put("type", savedDataItem.getType());
            dataItemMap.put("filePath", savedDataItem.getFilePath());
            dataItemMap.put("fileSize", savedDataItem.getFileSize());
            dataItemMap.put("fileType", savedDataItem.getFileType());
            dataItemMap.put("createdAt", savedDataItem.getCreatedAt().toString());
            
            response.put("dataItem", dataItemMap);

            return ResponseEntity.ok(response);
        } catch (Exception e) {
            e.printStackTrace();
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("error", "创建数据项失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
} 